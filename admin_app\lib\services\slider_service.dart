import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/slider_model.dart';

class SliderService {
  static const String _collection = 'sliders';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get all sliders ordered by order field
  static Future<List<SliderModel>> getAllSliders() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => SliderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting all sliders: $e');
      return [];
    }
  }

  /// Get only active sliders ordered by order field
  static Future<List<SliderModel>> getActiveSliders() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => SliderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting active sliders: $e');
      return [];
    }
  }

  /// Get slider by ID
  static Future<SliderModel?> getSliderById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        return SliderModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting slider by ID: $e');
      return null;
    }
  }

  /// Add new slider
  static Future<bool> addSlider(SliderModel slider) async {
    try {
      final docRef = _firestore.collection(_collection).doc();
      final sliderWithId = slider.copyWith(id: docRef.id);
      await docRef.set(sliderWithId.toMap());
      return true;
    } catch (e) {
      print('Error adding slider: $e');
      return false;
    }
  }

  /// Update existing slider
  static Future<bool> updateSlider(SliderModel slider) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(slider.id)
          .update(slider.toMap());
      return true;
    } catch (e) {
      print('Error updating slider: $e');
      return false;
    }
  }

  /// Delete slider
  static Future<bool> deleteSlider(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
      return true;
    } catch (e) {
      print('Error deleting slider: $e');
      return false;
    }
  }

  /// Get next order number for new slider
  static Future<int> getNextOrder() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('order', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final lastOrder = querySnapshot.docs.first.data()['order'] as int? ?? 0;
        return lastOrder + 1;
      }
      return 1;
    } catch (e) {
      print('Error getting next order: $e');
      return 1;
    }
  }

  /// Reorder sliders
  static Future<bool> reorderSliders(List<SliderModel> sliders) async {
    try {
      final batch = _firestore.batch();
      
      for (int i = 0; i < sliders.length; i++) {
        final slider = sliders[i];
        final updatedSlider = slider.copyWith(
          order: i + 1,
          updatedAt: DateTime.now(),
        );
        
        batch.update(
          _firestore.collection(_collection).doc(slider.id),
          updatedSlider.toMap(),
        );
      }
      
      await batch.commit();
      return true;
    } catch (e) {
      print('Error reordering sliders: $e');
      return false;
    }
  }

  /// Toggle slider active status
  static Future<bool> toggleSliderStatus(String id, bool isActive) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'isActive': isActive,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
      return true;
    } catch (e) {
      print('Error toggling slider status: $e');
      return false;
    }
  }

  /// Get sliders count
  static Future<int> getSlidersCount() async {
    try {
      final querySnapshot = await _firestore.collection(_collection).get();
      return querySnapshot.docs.length;
    } catch (e) {
      print('Error getting sliders count: $e');
      return 0;
    }
  }

  /// Get active sliders count
  static Future<int> getActiveSlidersCount() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();
      return querySnapshot.docs.length;
    } catch (e) {
      print('Error getting active sliders count: $e');
      return 0;
    }
  }

  /// Stream of all sliders for real-time updates
  static Stream<List<SliderModel>> slidersStream() {
    return _firestore
        .collection(_collection)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SliderModel.fromFirestore(doc))
            .toList());
  }

  /// Stream of active sliders for real-time updates
  static Stream<List<SliderModel>> activeSlidersStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SliderModel.fromFirestore(doc))
            .toList());
  }
}
