import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/navigation_models.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  // Navigation Groups
  List<NavigationGroup> getNavigationItems() {
    return [
      NavigationGroup(
        title: 'Dashboard',
        items: [
          NavigationItem(
            icon: Icons.dashboard_outlined,
            activeIcon: Icons.dashboard,
            label: 'Overview',
            index: 0,
            route: '/dashboard',
          ),
        ],
      ),
      NavigationGroup(
        title: 'User Management',
        items: [
          NavigationItem(
            icon: Icons.people_outline,
            activeIcon: Icons.people,
            label: 'Users',
            index: 1,
            route: '/users',
          ),
          NavigationItem(
            icon: Icons.verified_user_outlined,
            activeIcon: Icons.verified_user,
            label: 'Resellers',
            index: 2,
            route: '/resellers',
          ),
          NavigationItem(
            icon: Icons.admin_panel_settings_outlined,
            activeIcon: Icons.admin_panel_settings,
            label: 'Admins',
            index: 3,
            route: '/admins',
          ),
          NavigationItem(
            icon: Icons.person_add_outlined,
            activeIcon: Icons.person_add,
            label: 'Pending Registrations',
            index: 4,
            route: '/pending-registrations',
          ),
          NavigationItem(
            icon: Icons.store_outlined,
            activeIcon: Icons.store,
            label: 'Reseller Applications',
            index: 5,
            route: '/reseller-applications',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Content Moderation',
        items: [
          NavigationItem(
            icon: Icons.post_add_outlined,
            activeIcon: Icons.post_add,
            label: 'Posts',
            index: 6,
            route: '/moderation/posts',
          ),
          NavigationItem(
            icon: Icons.comment_outlined,
            activeIcon: Icons.comment,
            label: 'Comments',
            index: 7,
            route: '/moderation/comments',
          ),
          NavigationItem(
            icon: Icons.flag_outlined,
            activeIcon: Icons.flag,
            label: 'Reports',
            index: 8,
            route: '/moderation/reports',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Product Management',
        items: [
          NavigationItem(
            icon: Icons.shopping_bag_outlined,
            activeIcon: Icons.shopping_bag,
            label: 'Products',
            index: 9,
            route: '/products',
          ),
          NavigationItem(
            icon: Icons.pending_actions_outlined,
            activeIcon: Icons.pending_actions,
            label: 'Pending Products',
            index: 10,
            route: '/pending-products',
          ),
          NavigationItem(
            icon: Icons.category_outlined,
            activeIcon: Icons.category,
            label: 'Categories',
            index: 11,
            route: '/categories',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Support',
        items: [
          NavigationItem(
            icon: Icons.support_agent_outlined,
            activeIcon: Icons.support_agent,
            label: 'Support Management',
            index: 11,
            route: '/support',
          ),
        ],
      ),
      NavigationGroup(
        title: 'Marketing',
        items: [
          NavigationItem(
            icon: Icons.slideshow_outlined,
            activeIcon: Icons.slideshow,
            label: 'Slider Management',
            index: 12,
            route: '/sliders',
          ),
          NavigationItem(
            icon: Icons.people_outline,
            activeIcon: Icons.people,
            label: 'Referral Management',
            index: 13,
            route: '/referrals',
          ),
        ],
      ),
      NavigationGroup(
        title: 'System',
        items: [
          NavigationItem(
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: 'Settings',
            index: 14,
            route: '/settings',
          ),
        ],
      ),
    ];
  }

  // Get all navigation items flattened
  List<NavigationItem> getAllNavigationItems() {
    return getNavigationItems().expand((group) => group.items).toList();
  }

  // Get navigation item by route
  NavigationItem? getNavigationItemByRoute(String route) {
    return getAllNavigationItems().firstWhere(
      (item) => item.route == route,
      orElse: () => NavigationItem(
        icon: Icons.error_outline,
        activeIcon: Icons.error,
        label: 'Not Found',
        index: -1,
        route: '/not-found',
      ),
    );
  }
}
