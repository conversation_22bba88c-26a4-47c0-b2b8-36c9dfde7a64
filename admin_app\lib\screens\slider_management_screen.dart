import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/slider_model.dart';
import '../services/slider_service.dart';
import '../widgets/slider_management/slider_form_dialog.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';

class SliderManagementScreen extends StatefulWidget {
  const SliderManagementScreen({super.key});

  @override
  State<SliderManagementScreen> createState() => _SliderManagementScreenState();
}

class _SliderManagementScreenState extends State<SliderManagementScreen> {
  List<SliderModel> _sliders = [];
  bool _isLoading = true;
  bool _isReordering = false;

  @override
  void initState() {
    super.initState();
    _loadSliders();
  }

  Future<void> _loadSliders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final sliders = await SliderService.getAllSliders();
      if (mounted) {
        setState(() {
          _sliders = sliders;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('Error loading sliders: $e');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }

  Future<void> _showSliderDialog({SliderModel? slider}) async {
    final result = await showDialog<SliderModel>(
      context: context,
      builder: (context) => SliderFormDialog(slider: slider),
    );

    if (result != null) {
      bool success;
      if (slider == null) {
        // Adding new slider
        success = await SliderService.addSlider(result);
        if (success) {
          _showSuccessSnackBar('Slider added successfully');
        } else {
          _showErrorSnackBar('Failed to add slider');
        }
      } else {
        // Updating existing slider
        success = await SliderService.updateSlider(result);
        if (success) {
          _showSuccessSnackBar('Slider updated successfully');
        } else {
          _showErrorSnackBar('Failed to update slider');
        }
      }

      if (success) {
        _loadSliders();
      }
    }
  }

  Future<void> _deleteSlider(SliderModel slider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Slider'),
        content: Text('Are you sure you want to delete "${slider.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await SliderService.deleteSlider(slider.id);
      if (success) {
        _showSuccessSnackBar('Slider deleted successfully');
        _loadSliders();
      } else {
        _showErrorSnackBar('Failed to delete slider');
      }
    }
  }

  Future<void> _toggleSliderStatus(SliderModel slider) async {
    final success = await SliderService.toggleSliderStatus(
      slider.id,
      !slider.isActive,
    );

    if (success) {
      _showSuccessSnackBar(
        slider.isActive ? 'Slider deactivated' : 'Slider activated',
      );
      _loadSliders();
    } else {
      _showErrorSnackBar('Failed to update slider status');
    }
  }

  void _startReordering() {
    setState(() {
      _isReordering = true;
    });
  }

  Future<void> _saveReorder() async {
    setState(() {
      _isReordering = false;
    });

    final success = await SliderService.reorderSliders(_sliders);
    if (success) {
      _showSuccessSnackBar('Slider order updated successfully');
    } else {
      _showErrorSnackBar('Failed to update slider order');
      _loadSliders(); // Reload to reset order
    }
  }

  void _cancelReorder() {
    setState(() {
      _isReordering = false;
    });
    _loadSliders(); // Reload to reset order
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Slider Management (${_sliders.length})',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        actions: [
          if (_isReordering) ...[
            IconButton(
              icon: const Icon(Icons.check, color: Colors.white),
              onPressed: _saveReorder,
              tooltip: 'Save Order',
            ),
            IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: _cancelReorder,
              tooltip: 'Cancel',
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.reorder, color: Colors.white),
              onPressed: _sliders.isNotEmpty ? _startReordering : null,
              tooltip: 'Reorder Sliders',
            ),
            IconButton(
              icon: const Icon(Icons.add, color: Colors.white),
              onPressed: () => _showSliderDialog(),
              tooltip: 'Add Slider',
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadSliders,
              tooltip: 'Refresh',
            ),
          ],
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _isReordering
          ? null
          : FloatingActionButton(
              onPressed: () => _showSliderDialog(),
              backgroundColor: AppConstants.primaryColor,
              child: const Icon(Icons.add, color: Colors.white),
            ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_sliders.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.slideshow,
        title: 'No Sliders',
        subtitle: 'Add your first slider to get started.',
      );
    }

    return _isReordering ? _buildReorderableList() : _buildSlidersList();
  }

  Widget _buildSlidersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _sliders.length,
      itemBuilder: (context, index) {
        final slider = _sliders[index];
        return _buildSliderCard(slider);
      },
    );
  }

  Widget _buildReorderableList() {
    return ReorderableListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _sliders.length,
      onReorder: (oldIndex, newIndex) {
        setState(() {
          if (newIndex > oldIndex) {
            newIndex -= 1;
          }
          final item = _sliders.removeAt(oldIndex);
          _sliders.insert(newIndex, item);
        });
      },
      itemBuilder: (context, index) {
        final slider = _sliders[index];
        return _buildReorderableSliderCard(slider, index);
      },
    );
  }

  Widget _buildSliderCard(SliderModel slider) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      child: ListTile(
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            slider.imageUrl,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 60,
                height: 60,
                color: AppConstants.backgroundColor,
                child: const Icon(Icons.broken_image),
              );
            },
          ),
        ),
        title: Text(
          slider.title,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (slider.description != null)
              Text(
                slider.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: slider.isActive
                        ? AppConstants.successColor
                        : AppConstants.errorColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    slider.isActive ? 'Active' : 'Inactive',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Order: ${slider.order}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showSliderDialog(slider: slider);
                break;
              case 'toggle':
                _toggleSliderStatus(slider);
                break;
              case 'delete':
                _deleteSlider(slider);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'toggle',
              child: Row(
                children: [
                  Icon(
                    slider.isActive ? Icons.visibility_off : Icons.visibility,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(slider.isActive ? 'Deactivate' : 'Activate'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: AppConstants.errorColor),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: AppConstants.errorColor)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReorderableSliderCard(SliderModel slider, int index) {
    return Card(
      key: ValueKey(slider.id),
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      child: ListTile(
        leading: const Icon(Icons.drag_handle),
        title: Text(
          slider.title,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text('Order: ${index + 1}'),
        trailing: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            slider.imageUrl,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 60,
                height: 60,
                color: AppConstants.backgroundColor,
                child: const Icon(Icons.broken_image),
              );
            },
          ),
        ),
      ),
    );
  }
}
