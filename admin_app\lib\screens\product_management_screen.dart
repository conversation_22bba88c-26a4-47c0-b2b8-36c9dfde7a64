import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../widgets/product_management/product_card_admin.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';
import 'add_product_screen.dart';
import 'product_detail_screen.dart';

class ProductManagementScreen extends StatefulWidget {
  const ProductManagementScreen({super.key});

  @override
  State<ProductManagementScreen> createState() => _ProductManagementScreenState();
}

class _ProductManagementScreenState extends State<ProductManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Data
  List<ProductModel> _products = [];
  List<String> _categories = [];
  bool _isLoading = false;
  bool _hasMoreProducts = true;
  DocumentSnapshot? _lastDocument;
  String _currentFilter = 'all';
  String _selectedCategory = 'all';
  String _searchQuery = '';

  // Bulk operations
  Set<String> _selectedProducts = {};
  bool _isSelectionMode = false;

  // Statistics
  Map<String, int> _statistics = {
    'total': 0,
    'available': 0,
    'unavailable': 0,
    'featured': 0,
    'approved': 0,
    'pending': 0,
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _tabController.addListener(_onTabChanged);
    _scrollController.addListener(_onScroll);
    _loadStatistics();
    _loadCategories();
    _loadProducts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final filters = ['all', 'available', 'unavailable', 'featured', 'approved', 'pending'];
      _currentFilter = filters[_tabController.index];
      _clearSelection();
      _refreshProducts();
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreProducts();
    }
  }

  // Bulk operations methods
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedProducts.clear();
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedProducts.clear();
      _isSelectionMode = false;
    });
  }

  void _toggleProductSelection(String productId) {
    setState(() {
      if (_selectedProducts.contains(productId)) {
        _selectedProducts.remove(productId);
      } else {
        _selectedProducts.add(productId);
      }
    });
  }

  void _selectAllProducts() {
    setState(() {
      _selectedProducts = _products.map((p) => p.id).toSet();
    });
  }

  Future<void> _bulkApproveProducts() async {
    if (_selectedProducts.isEmpty) return;

    final confirmed = await _showConfirmationDialog(
      'Approve Products',
      'Are you sure you want to approve ${_selectedProducts.length} products?',
    );

    if (confirmed) {
      await _performBulkOperation(
        'Approving products...',
        (productId) => ProductService.approveProduct(
          productId: productId,
          moderatorNote: 'Bulk approved by admin',
        ),
      );
    }
  }

  Future<void> _bulkRejectProducts() async {
    if (_selectedProducts.isEmpty) return;

    final TextEditingController noteController = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Products'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Reject ${_selectedProducts.length} products?'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: noteController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter rejection reason...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      await _performBulkOperation(
        'Rejecting products...',
        (productId) => ProductService.rejectProduct(
          productId: productId,
          moderatorNote: result,
        ),
      );
    }
  }

  Future<void> _bulkDeleteProducts() async {
    if (_selectedProducts.isEmpty) return;

    final confirmed = await _showConfirmationDialog(
      'Delete Products',
      'Are you sure you want to delete ${_selectedProducts.length} products? This action cannot be undone.',
    );

    if (confirmed) {
      await _performBulkOperation(
        'Deleting products...',
        (productId) => ProductService.deleteProduct(productId),
      );
    }
  }

  Future<void> _performBulkOperation(
    String loadingMessage,
    Future<bool> Function(String) operation,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: AppConstants.paddingMedium),
            Text(loadingMessage),
          ],
        ),
      ),
    );

    int successCount = 0;
    final selectedProductIds = List.from(_selectedProducts);

    for (String productId in selectedProductIds) {
      try {
        final success = await operation(productId);
        if (success) successCount++;
      } catch (e) {
        print('Error in bulk operation for product $productId: $e');
      }
    }

    // Close loading dialog
    if (mounted) Navigator.of(context).pop();

    // Show result
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text('$successCount of ${selectedProductIds.length} products processed successfully'),
        backgroundColor: successCount > 0 ? AppConstants.successColor : AppConstants.errorColor,
      ),
    );

    // Refresh data
    _clearSelection();
    _refreshProducts();
    _loadStatistics();
  }

  Future<bool> _showConfirmationDialog(String title, String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  // Navigation methods
  Future<void> _navigateToAddProduct() async {
    final result = await Navigator.of(context).push<ProductModel>(
      MaterialPageRoute(
        builder: (context) => const AddProductScreen(),
      ),
    );

    if (result != null) {
      _refreshProducts();
      _loadStatistics();
    }
  }

  Future<void> _navigateToProductDetail(ProductModel product) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProductDetailScreen(product: product),
      ),
    );

    if (result != null) {
      _refreshProducts();
      _loadStatistics();
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await ProductService.getProductsStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
        });
      }
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await ProductService.getAllCategories();
      if (mounted) {
        setState(() {
          _categories = ['all', ...categories];
        });
      }
    } catch (e) {
      print('Error loading categories: $e');
    }
  }

  Future<void> _loadProducts() async {
    if (_isLoading || !_hasMoreProducts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final products = await ProductService.getAllProductsPaginated(
        lastDocument: _lastDocument,
        status: _currentFilter,
        category: _selectedCategory,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _products = products;
          } else {
            _products.addAll(products);
          }
          _hasMoreProducts = products.length >= 20;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('Error loading products: $e');
    }
  }

  Future<void> _loadMoreProducts() async {
    if (!_isLoading && _hasMoreProducts) {
      await _loadProducts();
    }
  }

  void _refreshProducts() {
    setState(() {
      _products.clear();
      _lastDocument = null;
      _hasMoreProducts = true;
    });
    _loadProducts();
    _loadStatistics();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _refreshProducts();
  }

  void _onCategoryChanged(String? category) {
    setState(() {
      _selectedCategory = category ?? 'all';
    });
    _refreshProducts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          _buildFilters(),
          if (_isSelectionMode) _buildBulkActionsBar(),
          Expanded(
            child: _buildProductsList(),
          ),
        ],
      ),
      floatingActionButton: _isSelectionMode ? null : FloatingActionButton(
        onPressed: _navigateToAddProduct,
        backgroundColor: AppConstants.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: _isSelectionMode
          ? Text('${_selectedProducts.length} selected')
          : const Text('Product Management'),
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      actions: [
        if (_isSelectionMode) ...[
          IconButton(
            onPressed: _selectAllProducts,
            icon: const Icon(Icons.select_all),
            tooltip: 'Select All',
          ),
          IconButton(
            onPressed: _clearSelection,
            icon: const Icon(Icons.close),
            tooltip: 'Clear Selection',
          ),
        ] else ...[
          IconButton(
            onPressed: _toggleSelectionMode,
            icon: const Icon(Icons.checklist),
            tooltip: 'Select Products',
          ),
          IconButton(
            onPressed: _refreshProducts,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ],
    );
  }

  Widget _buildBulkActionsBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: AppConstants.primaryColor,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '${_selectedProducts.length} products selected',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            onPressed: _selectedProducts.isNotEmpty ? _bulkApproveProducts : null,
            icon: const Icon(Icons.check, color: Colors.white),
            tooltip: 'Approve Selected',
          ),
          IconButton(
            onPressed: _selectedProducts.isNotEmpty ? _bulkRejectProducts : null,
            icon: const Icon(Icons.close, color: Colors.white),
            tooltip: 'Reject Selected',
          ),
          IconButton(
            onPressed: _selectedProducts.isNotEmpty ? _bulkDeleteProducts : null,
            icon: const Icon(Icons.delete, color: Colors.white),
            tooltip: 'Delete Selected',
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: const BoxDecoration(
        color: AppConstants.surfaceColor,
        border: Border(
          bottom: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.shopping_bag_outlined,
            size: AppConstants.iconSizeLarge,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          const Text(
            'Product Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeHeading,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _refreshProducts,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppConstants.surfaceColor,
      child: TabBar(
        controller: _tabController,
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        tabs: [
          Tab(
            text: 'All (${_statistics['total']})',
          ),
          Tab(
            text: 'Available (${_statistics['available']})',
          ),
          Tab(
            text: 'Unavailable (${_statistics['unavailable']})',
          ),
          Tab(
            text: 'Featured (${_statistics['featured']})',
          ),
          Tab(
            text: 'Approved (${_statistics['approved']})',
          ),
          Tab(
            text: 'Pending (${_statistics['pending']})',
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      color: AppConstants.surfaceColor,
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search products by name, description, category, or seller...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _onSearchChanged('');
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                borderSide: const BorderSide(color: AppConstants.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                borderSide: const BorderSide(color: AppConstants.primaryColor),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          // Category filter
          Row(
            children: [
              const Text(
                'Category:',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  items: _categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(
                        category == 'all' ? 'All Categories' : category,
                        style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
                      ),
                    );
                  }).toList(),
                  onChanged: _onCategoryChanged,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList() {
    if (_isLoading && _products.isEmpty) {
      return const LoadingWidget();
    }

    if (_products.isEmpty && !_isLoading) {
      return EmptyStateWidget(
        icon: Icons.shopping_bag_outlined,
        title: 'No Products Found',
        subtitle: _searchQuery.isNotEmpty
            ? 'No products match your search criteria'
            : 'No products available for the selected filter',
        actionText: 'Refresh',
        onAction: _refreshProducts,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshProducts();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _products.length + (_hasMoreProducts ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _products.length) {
            return const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final product = _products[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: GestureDetector(
              onTap: _isSelectionMode
                  ? () => _toggleProductSelection(product.id)
                  : () => _navigateToProductDetail(product),
              child: Stack(
                children: [
                  ProductCardAdmin(
                    product: product,
                    onProductUpdated: () {
                      _refreshProducts();
                      _loadStatistics();
                    },
                    isSelectionMode: _isSelectionMode,
                    isSelected: _selectedProducts.contains(product.id),
                    onSelectionChanged: (selected) {
                      if (selected) {
                        _selectedProducts.add(product.id);
                      } else {
                        _selectedProducts.remove(product.id);
                      }
                      setState(() {});
                    },
                  ),
                  if (_isSelectionMode)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _selectedProducts.contains(product.id)
                              ? AppConstants.primaryColor
                              : Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          Icons.check,
                          color: _selectedProducts.contains(product.id)
                              ? Colors.white
                              : Colors.transparent,
                          size: 20,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
