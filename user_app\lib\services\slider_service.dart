import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/slider_model.dart';

class SliderService {
  static const String _collection = 'sliders';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get only active sliders ordered by order field
  static Future<List<SliderModel>> getActiveSliders() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => SliderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting active sliders: $e');
      return [];
    }
  }

  /// Get slider by ID
  static Future<SliderModel?> getSliderById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        return SliderModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting slider by ID: $e');
      return null;
    }
  }

  /// Get active sliders count
  static Future<int> getActiveSlidersCount() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();
      return querySnapshot.docs.length;
    } catch (e) {
      print('Error getting active sliders count: $e');
      return 0;
    }
  }

  /// Stream of active sliders for real-time updates
  static Stream<List<SliderModel>> activeSlidersStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SliderModel.fromFirestore(doc))
            .toList());
  }
}
